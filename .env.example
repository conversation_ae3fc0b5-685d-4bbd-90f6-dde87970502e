################################################################

NODE_ENV=production
DATABASE_URL=**************************************/app
DATABASE_URL=**************************************/app
INGESTION_API_URL=http://api:8000
ANALYSIS_DB_DIR=/ingested_repos

################################################################

# You must set the following environment variable for chat to work
OPENAI_API_KEY=<your_openai_api_key>

# Optional: GitHub token to access private repositories
GITHUB_TOKEN=<your_github_token>

# The following are customizable for summaries and embeddings (OpenAI-compatible APIs)

SUMMARIES_BASE_URL=https://openrouter.ai/api/v1
SUMMARIES_MODEL=google/gemini-2.5-flash
SUMMARIES_API_KEY=<your_summaries_api_key>

# Default Gemini rate limits
MAX_REQUESTS_PER_SECOND=15

EMBEDDINGS_BASE_URL=https://api.openai.com/v1
EMBEDDINGS_MODEL=text-embedding-3-large
EMBEDDINGS_API_KEY=<your_embeddings_api_key>
