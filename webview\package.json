{"name": "sita-onboard-faster", "version": "0.1.0", "private": true, "lint-staged": {"*.{js,jsx,ts,tsx,md,mdx,css,scss,html,json,yml,yaml}": ["prettier --write"], "*.{ts,tsx,js,jsx}": ["eslint --cache --fix"]}, "scripts": {"build": "turbo build", "dev": "turbo dev", "start": "turbo start", "lint": "turbo lint"}, "dependencies": {}, "packageManager": "pnpm@10.15.0", "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@next/eslint-plugin-next": "^15.4.6", "eslint-config-next": "15.4.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.2.0", "prettier-plugin-tailwindcss": "^0.6.14", "turbo": "^2.5.6", "typescript": "^5.9.2"}}