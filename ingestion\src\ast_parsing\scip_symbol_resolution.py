"""Simplified SCIP parser for cross-file reference tracking.

This module extracts symbols and references from SCIP indexes to complement
tree-sitter AST parsing with cross-file relationship data.
"""

from __future__ import annotations
import os
import re
import subprocess
import sys
import tempfile
import shutil
from dataclasses import dataclass
from typing import Optional

# Generated by protoc from scip.proto (see setup above)
import ast_parsing.scip_pb2 as scip_pb2


@dataclass
class ScipSymbol:
    """Minimal symbol info from SCIP for mapping to tree-sitter definitions."""

    symbol: str
    name: str
    file: str  # repo-relative
    range: tuple[int, int, int, int]  # (startLine, startChar, endLine, endChar) 0-based
    container_symbol: str | None  # e.g., class for a method


@dataclass
class ScipReference:
    """Reference to a symbol from SCIP."""

    symbol: str
    file: str
    range: tuple[int, int, int, int]
    role_bits: int  # raw bitset from SCIP
    # Convenience annotations populated from role bits and symbol map
    is_definition: bool = False  # True iff Definition role bit is set
    # If resolvable, file where the referenced symbol is defined (repo-relative)
    to_file: str | None = None
    # If known, whether this occurrence is a cross-file outgoing reference
    # (i.e., not a definition and ref.file != to_file). None if unknown.
    is_outgoing: bool | None = None


@dataclass
class FileSymbols:
    symbols: list[ScipSymbol]
    references: list[ScipReference]


@dataclass
class ScipResult:
    files: dict[str, FileSymbols]  # path -> symbols/refs in that file
    symbol_to_info: dict[str, ScipSymbol]  # symbol -> basic info
    symbol_to_references: dict[str, list[ScipReference]]  # symbol -> refs
    edges_intra_repo: list[tuple[str, str, str]]  # (from_file, to_file, symbol)


_SEPS = "/#.:!"
_ROLE_DEFINITION = 0x1  # matches scip.proto: SymbolRole.Definition


def _unescape_bt(s: str) -> str:
    # Backtick-escaped identifiers use `` to mean a literal `
    if len(s) >= 2 and s[0] == s[-1] == "`":
        return s[1:-1].replace("``", "`")
    return s


def _split_last_descriptor(symbol: str) -> tuple[str, str] | None:
    """
    Return (base_name_without_disamb, suffix_char) for the last descriptor.
    Handles methods (name(params).), plain type (#), term/field (.), namespace (/), meta (:).
    """
    # find index of last suffix marker
    i = max(symbol.rfind(c) for c in _SEPS)
    if i == -1:
        return None
    suffix = symbol[i]  # '#', '.', '/', ':', or '!'
    head = symbol[:i]  # everything before the suffix
    # slice out the token between previous separator and here
    j = max(head.rfind(c) for c in _SEPS)
    token = head[j + 1 :] if j != -1 else head

    # method: "... name(args) ."
    if (
        suffix == "."
        and ")" in token
        and "(" in token
        and token.rfind(")") == len(token) - 1
    ):
        token = token[: token.rfind("(")]  # strip "(...)"

    # unescape backticks
    token = _unescape_bt(token)

    # strip trailing numeric disambiguators: value0, headers1, etc.
    base = re.sub(r"\d+$", "", token)
    return base, suffix


def _pretty_name(si) -> str:
    dn = getattr(si, "display_name", "") or ""
    if dn:
        return dn  # recommended by SCIP; symbol text isn't reliable for UI labels
    res = _split_last_descriptor(getattr(si, "symbol", ""))
    return res[0] if res else ""


def collect_repo_symbols_with_scip(
    repo_root: str,
    out_dir: Optional[str] = None,
    workspace_type: Optional[str] = None,
) -> ScipResult:
    """
    Index a repo with SCIP indexers, parse all `.scip` outputs, and return:
      - selected definitions (top-level variables, classes/interfaces/enums, funcs/methods, types/aliases)
      - reference occurrences, grouped by symbol
      - intra-repo dependency edges (file -> def.file for referenced symbols)
    External symbols are marked is_external=True.

    `languages` may include: "typescript", "javascript", "python", "go", "rust",
                             "java", "scala", "kotlin", "c", "cpp", "csharp", "dotnet", "dart", "php", "ruby".
    If None, we infer from file extensions present.
    """
    repo_root = os.path.abspath(repo_root)
    if not os.path.isdir(repo_root):
        raise ValueError(f"repo_root does not exist: {repo_root}")

    tmp = tempfile.mkdtemp(prefix="scip-idx-") if out_dir is None else out_dir
    os.makedirs(tmp, exist_ok=True)

    try:
        langs = _infer_languages(repo_root)

        print(f"[scip] detected languages: {langs}")
        scip_paths: list[str] = []

        # 1) Run language indexers (best-effort; skip missing tools)
        for lang in sorted(langs):
            out_path = os.path.join(tmp, f"index.{lang}.scip")
            cmd, fbk = _index_command(
                language=lang, out_path=out_path, workspace_type=workspace_type
            )
            if not cmd:
                continue
            try:
                print(f"[scip] indexing {lang}...")
                _ = subprocess.run(
                    cmd,
                    cwd=repo_root,
                    check=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                )
                if os.path.exists(out_path):
                    scip_paths.append(out_path)
            except Exception as e:
                print(f"[scip] indexing {lang} failed: {e}, using fallback...")
                if fbk:
                    try:
                        _ = subprocess.run(
                            fbk,
                            cwd=repo_root,
                            check=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                        )
                        if os.path.exists(out_path):
                            scip_paths.append(out_path)
                        continue  # success with fallback
                    except Exception:
                        raise e  # fall through to outer handler

                # Non-fatal: skip languages we can't index in this environment
                print(f"[scip] skipping {lang}: {e}", file=sys.stderr)
                raise e

        if not scip_paths:
            raise RuntimeError(
                "No SCIP indexes were produced. Check indexers/tooling availability."
            )

        # 2) Parse & merge all SCIP Index messages
        merged = scip_pb2.Index()  # pyright: ignore[reportUnknownMemberType, reportAttributeAccessIssue]
        first_meta = None
        for p in scip_paths:
            idx = _read_scip(p)
            if first_meta is None and hasattr(idx, "metadata"):
                first_meta = idx.metadata
                merged.metadata.CopyFrom(first_meta)
            merged.documents.extend(idx.documents)
            if hasattr(idx, "external_symbols"):
                merged.external_symbols.extend(
                    idx.external_symbols
                )  # may not exist in older schema

        # 3) Build symbol maps
        # We collect definitions strictly from occurrences with the Definition role bit
        defined: dict[str, ScipSymbol] = {}
        files: dict[str, FileSymbols] = {}
        symbol_to_refs: dict[str, list[ScipReference]] = {}
        # Temporary stores while scanning documents
        def_occurrence: dict[
            str, tuple[str, tuple[int, int, int, int]]
        ] = {}  # symbol -> (def_file, range)
        symbol_names: dict[str, str] = {}  # best-effort display names

        # print("documents", len(merged.documents))

        for doc in merged.documents:
            path = doc.relative_path

            file_bucket = files.setdefault(path, FileSymbols(symbols=[], references=[]))

            # Record best-effort display names for symbols
            doc_symbols = list(getattr(doc, "symbols", []))
            for sym in doc_symbols:
                s = getattr(sym, "symbol", "")
                if not s:
                    continue
                if s not in symbol_names:
                    name = _pretty_name(sym)
                    if not name:
                        res = _split_last_descriptor(s)
                        if res:
                            name = res[0]
                    symbol_names[s] = name or ""
            # Quick lookup: which symbols are declared in this document
            declared_here = {getattr(sym, "symbol", "") for sym in doc_symbols if getattr(sym, "symbol", "")}

            # references/occurrences
            # Track a simple preference score for where we capture the def occurrence:
            # 2 = this document declares the symbol (preferred), 1 = plain def occurrence.
            def_pref_score: dict[str, int] = {}

            for occ in getattr(doc, "occurrences", []):
                s = getattr(occ, "symbol", "")
                if not s or s.startswith("local "):
                    continue
                role_bits = getattr(occ, "symbol_roles", 0)
                rng = _occurrence_range4(occ)

                ref = ScipReference(
                    symbol=s,
                    file=path,
                    range=rng,
                    role_bits=role_bits,
                    is_definition=bool(role_bits & _ROLE_DEFINITION),
                )
                file_bucket.references.append(ref)
                symbol_to_refs.setdefault(s, []).append(ref)

                # Capture true definition locations strictly from occurrences
                if (role_bits & _ROLE_DEFINITION) != 0:
                    # Prefer a def occurrence inside a document that actually declares the symbol
                    new_score = 2 if s in declared_here else 1
                    old_score = def_pref_score.get(s, 0)
                    if s not in def_occurrence or new_score > old_score:
                        def_occurrence[s] = (path, rng)
                        def_pref_score[s] = new_score

        # Materialize symbol_to_info and per-file symbol lists from captured defs
        for s, (def_file, occ_range) in def_occurrence.items():
            name = symbol_names.get(s)
            if not name:
                res = _split_last_descriptor(s)
                name = res[0] if res else ""

            container = None
            if "#" in s:
                container = s.split("#", 1)[0]

            d = ScipSymbol(
                symbol=s,
                name=name or "",
                file=def_file,
                range=occ_range,
                container_symbol=container,
            )
            defined[s] = d
            files.setdefault(def_file, FileSymbols(symbols=[], references=[])).symbols.append(d)

        # 4) Build simple intra-repo edges (file → file for referenced symbol)
        edges: list[tuple[str, str, str]] = []
        for s, refs in symbol_to_refs.items():
            if s in defined:
                target_info = defined[s]
                for ref in refs:
                    # Skip definitions and same-file occurrences
                    if (ref.role_bits & _ROLE_DEFINITION) != 0:
                        continue
                    # Annotate convenience direction fields on the occurrence
                    ref.to_file = target_info.file
                    ref.is_outgoing = ref.file != target_info.file
                    if ref.file != target_info.file:
                        edges.append((ref.file, target_info.file, s))

        return ScipResult(
            files=files,
            symbol_to_info=defined,
            symbol_to_references=symbol_to_refs,
            edges_intra_repo=edges,
        )

    except Exception as e:
        print(f"Error processing: {e}")
        raise e

    finally:
        if out_dir is None:
            shutil.rmtree(tmp, ignore_errors=True)


def _read_scip(path: str) -> scip_pb2.Index:  # pyright: ignore[reportUnknownMemberType, reportAttributeAccessIssue]
    """Read and parse a SCIP index file."""
    with open(path, "rb") as f:
        data = f.read()
    idx = scip_pb2.Index()  # pyright: ignore[reportUnknownMemberType, reportAttributeAccessIssue]
    idx.ParseFromString(data)
    return idx


def _infer_languages(root: str) -> list[str]:
    """Infer languages from file extensions in the repository."""
    exts = set()
    for dp, _, files in os.walk(root):
        # Skip common build/vendor directories
        if any(
            x in dp
            for x in (
                "/node_modules",
                "/.git",
                "/.idea",
                "/target",
                "/build",
                "/dist",
                "/.venv",
                "/venv",
            )
        ):
            continue
        for f in files:
            _, ext = os.path.splitext(f)
            exts.add(ext.lower())

    langs = set()
    if exts & {".ts", ".tsx", ".js", ".jsx"}:
        langs |= {"typescript"}
    if exts & {".py"}:
        langs |= {"python"}
    if exts & {".go"}:
        langs |= {"go"}
    if exts & {".rs"}:
        langs |= {"rust"}
    if exts & {".java", ".kt", ".scala"}:
        langs |= {"java"}
    if exts & {".c", ".cc", ".cpp", ".cxx", ".hpp", ".h"}:
        langs |= {"cpp"}
    if exts & {".cs", ".vb"}:
        langs |= {"dotnet"}
    if exts & {".dart"}:
        langs |= {"dart"}
    if exts & {".php"}:
        langs |= {"php"}
    if exts & {".rb"}:
        langs |= {"ruby"}
    return sorted(langs)


def _index_command(
    language: str, out_path: str, workspace_type: Optional[str] = None
) -> tuple[Optional[list[str]], Optional[list[str]]]:
    """Generate subprocess command for SCIP indexer."""

    def wrap(cmd: list[str]) -> list[str]:
        return [
            "bash",
            "-lc",
            f"{' '.join(cmd)} && test -f index.scip && mv index.scip '{out_path}'",
        ]

    if language in {"typescript", "javascript"}:
        cmd = ["scip-typescript", "index"]

        fallback = ["scip-typescript", "index", "--infer-tsconfig"]

        # Add workspace-specific flags
        if workspace_type == "pnpm":
            cmd.append("--pnpm-workspaces")
        elif workspace_type == "yarn":
            cmd.append("--yarn-workspaces")

        # For JavaScript projects, add --infer-tsconfig
        if language == "javascript":
            cmd.append("--infer-tsconfig")

        return wrap(cmd), fallback
    if language == "python":
        return wrap(["scip-python", "index", "."]), None
    # if language == "go":
    #     return wrap(["scip-go", "index", "."]), None
    # if language == "rust":
    #     return wrap(["rust-analyzer", "scip", "."]), None
    # if language == "java":
    #     return wrap(["scip-java", "index"]), None
    # if language in {"c", "cpp"}:
    #     return wrap(["scip-clang", "index", "."]), None
    # if language in {"dotnet", "csharp"}:
    #     return wrap(["scip-dotnet", "index"]), None
    # if language == "dart":
    #     return wrap(["scip-dart", "index"]), None
    # if language == "php":
    #     return wrap(["scip-php", "index"]), None
    # if language == "ruby":
    #     return wrap(["scip-ruby", "index"]), None
    raise ValueError(f"Unsupported language for SCIP indexing: {language}")


def _occurrence_range4(occ) -> tuple[int, int, int, int]:
    """Extract occurrence range as (startLine, startChar, endLine, endChar)."""
    r = getattr(occ, "range", None)
    if r is None:
        return (0, 0, 0, 0)

    # Newer schema: message Range { repeated int32 start; repeated int32 end; }
    if hasattr(r, "start") and hasattr(r, "end"):
        start = list(getattr(r, "start", [])) or [0, 0]
        end = list(getattr(r, "end", [])) or start
        sL, sC = (start + [0, 0])[:2]
        eL, eC = (end + [sL, sC])[:2]
        return (sL, sC, eL, eC)

    # Older schema: repeated int32 range
    try:
        arr = list(r)
    except TypeError:
        return (0, 0, 0, 0)

    if arr:
        if len(arr) == 4:
            return tuple(arr)
        if len(arr) == 3:
            sL, sC, eC = arr
            return (sL, sC, sL, eC)

    return (0, 0, 0, 0)


def _find_def_occurrence_and_container(
    doc, symbol: str
) -> tuple[tuple[int, int, int, int], str | None]:
    """Find definition occurrence and container for a symbol."""
    def_role = 1  # Definition role bit
    first_rng = (0, 0, 0, 0)
    container = None

    for occ in getattr(doc, "occurrences", []):
        if occ.symbol != symbol:
            continue
        rng = _occurrence_range4(occ)
        if not any(first_rng):
            first_rng = rng
        if getattr(occ, "symbol_roles", 0) & def_role:
            first_rng = rng
            break

    # Try to infer container from symbol structure
    if "#" in symbol:
        container = symbol.split("#", 1)[0]

    return first_rng, container
