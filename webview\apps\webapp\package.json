{"name": "@sita/onboard-webapp", "version": "0.1.0", "private": true, "lint-staged": {"*.{js,jsx,ts,tsx,md,mdx,css,scss,html,json,yml,yaml}": ["prettier --write"], "*.{ts,tsx,js,jsx}": ["eslint --cache --fix"]}, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier . --write", "format:check": "prettier . --check", "prepare": "husky", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "generate-api-types": "npx openapi-typescript http://localhost:8000/schema --output ./src/types/api.ts --prettify"}, "dependencies": {"@aws-sdk/client-s3": "^3.873.0", "@aws-sdk/client-sqs": "^3.864.0", "@aws-sdk/lib-storage": "^3.873.0", "@hookform/resolvers": "^5.2.1", "@libsql/client": "^0.15.15", "@modelcontextprotocol/sdk": "^1.17.4", "@monaco-editor/react": "^4.7.0", "@opennextjs/cloudflare": "^1.6.5", "@pinecone-database/pinecone": "^6.1.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@sita/shared": "workspace:*", "@tailwindcss/oxide-linux-arm64-gnu": "4.1.13", "@tanstack/react-query": "^5.85.3", "@tanstack/react-query-devtools": "^5.85.3", "@trpc/client": "^11.4.4", "@trpc/server": "^11.4.4", "@trpc/tanstack-react-query": "^11.4.4", "@types/archiver": "^6.0.3", "@types/canvas-confetti": "^1.9.0", "@types/dagre": "^0.7.53", "@xyflow/react": "^12.8.3", "archiver": "^7.0.1", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "cloudflare": "^4.5.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dagre": "^0.8.5", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "events": "^3.3.0", "framer-motion": "^12.23.12", "i18next": "^25.3.6", "i18next-browser-languagedetector": "^8.2.0", "input-otp": "^1.4.2", "jose": "^6.0.12", "jotai": "^2.13.1", "jotai-tanstack-query": "^0.11.0", "lightningcss-linux-arm64-gnu": "1.30.1", "lodash.debounce": "^4.0.8", "lucide-react": "^0.539.0", "mcp-handler": "^1.0.2", "mdast-util-to-string": "^4.0.0", "monaco-editor": "^0.52.2", "next": "15.4.5", "next-themes": "^0.4.6", "openai": "^5.12.2", "postgres": "^3.4.7", "react": "^19.1.1", "react-accessible-treeview": "^2.11.2", "react-dom": "^19.1.1", "react-draggable": "^4.5.0", "react-hook-form": "^7.62.0", "react-i18next": "^15.6.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.4", "recharts": "^3.1.2", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.7", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "uuid": "^11.1.0", "zod": "~3.25.76", "zod-to-json-schema": "^3.24.6"}, "packageManager": "pnpm@10.15.0", "optionalDependencies": {"libsql": "0.5.22", "@libsql/linux-x64-gnu": "0.5.22", "@libsql/linux-arm64-gnu": "0.5.22", "@libsql/darwin-arm64": "0.5.22"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@next/eslint-plugin-next": "^15.4.6", "@tailwindcss/postcss": "^4.1.12", "@tailwindcss/typography": "^0.5.16", "@types/lodash.debounce": "^4.0.9", "@types/mdast": "^4.0.4", "@types/node": "^24.3.0", "@types/pg": "^8.15.5", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.31.4", "eslint": "^9.33.0", "eslint-config-next": "15.4.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "openapi-typescript": "^7.9.1", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.2.0", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.12", "turbo": "^2.5.6", "tw-animate-css": "^1.3.7", "typescript": "^5.9.2", "wrangler": "^4.30.0"}}