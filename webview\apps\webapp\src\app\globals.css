@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-primary-accent: var(--primary-accent);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* Mid-century light mode colors */
  --background: oklch(0.96 0.01 85); /* parchment white #f5f2eb */
  --foreground: oklch(0.25 0 0); /* graphite #333333 */
  --card: oklch(1 0 0); /* white #ffffff */
  --card-foreground: oklch(0.25 0 0); /* graphite #333333 */
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.25 0 0);
  --primary: oklch(0.55 0.12 180); /* teal accent #008b8b */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.7463 0.1357 84.93); /* mustard accent #d4a534 */
  --secondary-foreground: oklch(0.25 0 0);
  --muted: oklch(0.94 0.01 85); /* lighter parchment */
  --muted-foreground: oklch(0.45 0 0); /* text secondary #666666 */
  --accent: oklch(0.75 0.08 85); /* mustard accent */
  --accent-foreground: oklch(0.25 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0.01 85);
  --input: oklch(0.98 0.005 85);
  --ring: oklch(0.55 0.12 180); /* teal ring */
  --chart-1: oklch(0.55 0.12 180); /* teal */
  --chart-2: oklch(0.75 0.08 85); /* mustard */
  --chart-3: oklch(0.65 0.06 45); /* warm brown */
  --chart-4: oklch(0.7 0.04 120); /* sage green */
  --chart-5: oklch(0.6 0.08 30); /* terracotta */
  --sidebar: oklch(0.98 0.005 85);
  --sidebar-foreground: oklch(0.25 0 0);
  --sidebar-primary: oklch(0.55 0.12 180);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.94 0.01 85);
  --sidebar-accent-foreground: oklch(0.25 0 0);
  --sidebar-border: oklch(0.9 0.01 85);
  --sidebar-ring: oklch(0.55 0.12 180);
}

.dark {
  --radius: 0.625rem;
  --background: #0d1117; /* deep slate */
  --foreground: #e5e7eb; /* zinc-200 */
  --card: #0f141b; /* darker panel */
  --card-foreground: #e5e7eb;
  --popover: #0f141b;
  --popover-foreground: #e5e7eb;
  --primary: #8b5cf6; /* indigo-500 */
  --primary-foreground: #ffffff;
  --secondary: #10b981; /* emerald-500 */
  --secondary-foreground: #0d1117;
  --muted: #111827; /* slate-900 approx */
  --muted-foreground: #9ca3af; /* gray-400 */
  --accent: #1f2937; /* slate-800 */
  --accent-foreground: #e5e7eb;
  --destructive: #dc2626;
  --border: rgba(255, 255, 255, 0.08);
  --input: rgba(255, 255, 255, 0.05);
  --ring: #8b5cf6;
  --chart-1: #8b5cf6; /* indigo */
  --chart-2: #10b981; /* emerald */
  --chart-3: #06b6d4; /* cyan */
  --chart-4: #f59e0b; /* amber */
  --chart-5: #ef4444; /* red */
  --sidebar: #0f141b;
  --sidebar-foreground: #e5e7eb;
  --sidebar-primary: #8b5cf6;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #111827;
  --sidebar-accent-foreground: #e5e7eb;
  --sidebar-border: rgba(255, 255, 255, 0.08);
  --sidebar-ring: #8b5cf6;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family:
      -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text",
      system-ui, sans-serif;
  }

  :where(button, [role="button"]):not([disabled]) {
    cursor: pointer;
  }

  /* Apply Apple's system font to all text elements */

  /* h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  span,
  div,
  a,
  button,
  input,
  textarea,
  select,
  label {
    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display",
      "SF Pro Text", system-ui, sans-serif;
  } */

  .markdown > * {
    all: revert;
  }

  .markdown pre {
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
  }

  @keyframes marquee-right {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    } /* slide to the 1st copy */
  }

  .marquee {
    display: flex;
    width: max-content; /* shrink to content so -50% is exactly one copy */
    animation: marquee-right var(--marquee-duration, 36s) linear infinite;
  }

  @media (prefers-reduced-motion: reduce) {
    .marquee {
      animation: none;
    }
  }

  /* Ensure anchor/scrollIntoView accounts for fixed header height */
  html {
    scroll-padding-top: 56px; /* matches header content height on mobile (h-14) */
  }
  @media (min-width: 640px) {
    /* sm */
    html {
      scroll-padding-top: 64px; /* matches header content height on sm+ (h-16) */
    }
  }

  /* ReactFlow dark mode overrides */
  .dark .react-flow__controls {
    background: var(--card) !important;
    border: 1px solid var(--border) !important;
  }

  .dark .react-flow__controls-button {
    background: var(--card) !important;
    border: 1px solid var(--border) !important;
    color: var(--foreground) !important;
  }

  .dark .react-flow__controls-button:hover {
    background: var(--muted) !important;
  }

  .dark .react-flow__minimap {
    background: var(--card) !important;
    border: 1px solid var(--border) !important;
  }

  .dark .react-flow__panel {
    background: var(--card) !important;
    border: 1px solid var(--border) !important;
    color: var(--foreground) !important;
  }

  /* Invert specific SVG logos in dark mode */
  .dark img[src="/github.svg"],
  .dark img[src="/linear.svg"],
  .dark img[src="/notion.svg"] {
    filter: invert(1);
  }
}
