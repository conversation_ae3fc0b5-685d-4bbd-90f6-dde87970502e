{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "7", "dialect": "postgresql", "tables": {"public.apikey": {"name": "apikey", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "start": {"name": "start", "type": "text", "primaryKey": false, "notNull": false}, "prefix": {"name": "prefix", "type": "text", "primaryKey": false, "notNull": false}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "refill_interval": {"name": "refill_interval", "type": "integer", "primaryKey": false, "notNull": false}, "refill_amount": {"name": "refill_amount", "type": "integer", "primaryKey": false, "notNull": false}, "last_refill_at": {"name": "last_refill_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "rate_limit_enabled": {"name": "rate_limit_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "rate_limit_time_window": {"name": "rate_limit_time_window", "type": "integer", "primaryKey": false, "notNull": false, "default": 86400000}, "rate_limit_max": {"name": "rate_limit_max", "type": "integer", "primaryKey": false, "notNull": false, "default": 10}, "request_count": {"name": "request_count", "type": "integer", "primaryKey": false, "notNull": false}, "remaining": {"name": "remaining", "type": "integer", "primaryKey": false, "notNull": false}, "last_request": {"name": "last_request", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "permissions": {"name": "permissions", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"apikey_user_id_user_id_fk": {"name": "apikey_user_id_user_id_fk", "tableFrom": "apikey", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.extension_tokens": {"name": "extension_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "token_hash": {"name": "token_hash", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "'Extension Token'"}, "last_used_at": {"name": "last_used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'unknown'"}}, "indexes": {}, "foreignKeys": {"extension_user_id_fkey": {"name": "extension_user_id_fkey", "tableFrom": "extension_tokens", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"extension_tokens_token_hash_key": {"columns": ["token_hash"], "nullsNotDistinct": false, "name": "extension_tokens_token_hash_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.anonymous_users": {"name": "anonymous_users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "last_message_date": {"name": "last_message_date", "type": "date", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "fingerprint": {"name": "fingerprint", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"anonymous_users_fingerprint_unique": {"columns": ["fingerprint"], "nullsNotDistinct": false, "name": "anonymous_users_fingerprint_unique"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.invitation": {"name": "invitation", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "inviter_id": {"name": "inviter_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"invitation_inviter_id_user_id_fk": {"name": "invitation_inviter_id_user_id_fk", "tableFrom": "invitation", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["inviter_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "invitation_organization_id_organization_id_fk": {"name": "invitation_organization_id_organization_id_fk", "tableFrom": "invitation", "tableTo": "organization", "schemaTo": "public", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.member": {"name": "member", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'member'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"member_organization_id_organization_id_fk": {"name": "member_organization_id_organization_id_fk", "tableFrom": "member", "tableTo": "organization", "schemaTo": "public", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "member_user_id_user_id_fk": {"name": "member_user_id_user_id_fk", "tableFrom": "member", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.message_limits": {"name": "message_limits", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "anonymous_user_id": {"name": "anonymous_user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": true}, "limit": {"name": "limit", "type": "integer", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"message_limits_anonymous_user_id_anonymous_users_id_fk": {"name": "message_limits_anonymous_user_id_anonymous_users_id_fk", "tableFrom": "message_limits", "tableTo": "anonymous_users", "schemaTo": "public", "columnsFrom": ["anonymous_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "message_limits_user_id_user_id_fk": {"name": "message_limits_user_id_user_id_fk", "tableFrom": "message_limits", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {"limits_user_check": {"name": "limits_user_check", "value": "((user_id IS NOT NULL) AND (anonymous_user_id IS NULL)) OR ((user_id IS NULL) AND (anonymous_user_id IS NOT NULL))"}}, "policies": {}, "isRLSEnabled": false}, "public.leads": {"name": "leads", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "company_url": {"name": "company_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "repository_count": {"name": "repository_count", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "pain_point": {"name": "pain_point", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"leads_email_key": {"columns": ["email"], "nullsNotDistinct": false, "name": "leads_email_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.conversations": {"name": "conversations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "character_count": {"name": "character_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "anonymous_user_id": {"name": "anonymous_user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"idx_conversations_anonymous_user_id": {"name": "idx_conversations_anonymous_user_id", "columns": [{"expression": "anonymous_user_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_conversations_project_id": {"name": "idx_conversations_project_id", "columns": [{"expression": "project_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"conversations_anonymous_user_id_fkey": {"name": "conversations_anonymous_user_id_fkey", "tableFrom": "conversations", "tableTo": "anonymous_users", "schemaTo": "public", "columnsFrom": ["anonymous_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "conversations_project_id_fkey": {"name": "conversations_project_id_fkey", "tableFrom": "conversations", "tableTo": "public_projects", "schemaTo": "public", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "conversations_user_id_fkey": {"name": "conversations_user_id_fkey", "tableFrom": "conversations", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {"conversations_user_check": {"name": "conversations_user_check", "value": "((user_id IS NOT NULL) AND (anonymous_user_id IS NULL)) OR ((user_id IS NULL) AND (anonymous_user_id IS NOT NULL))"}}, "policies": {}, "isRLSEnabled": false}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "conversation_id": {"name": "conversation_id", "type": "uuid", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "thought_content": {"name": "thought_content", "type": "text", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "included_in_context": {"name": "included_in_context", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "character_count": {"name": "character_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {"messages_conversation_id_fkey": {"name": "messages_conversation_id_fkey", "tableFrom": "messages", "tableTo": "conversations", "schemaTo": "public", "columnsFrom": ["conversation_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {"messages_role_check": {"name": "messages_role_check", "value": "role = ANY (ARRAY['user'::text, 'assistant'::text])"}}, "policies": {}, "isRLSEnabled": false}, "public.organization": {"name": "organization", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organization_slug_unique": {"columns": ["slug"], "nullsNotDistinct": false, "name": "organization_slug_unique"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.rate_limit": {"name": "rate_limit", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": false}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": false}, "last_request": {"name": "last_request", "type": "bigint", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.repos": {"name": "repos", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "repository_url": {"name": "repository_url", "type": "text", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "db_url": {"name": "db_url", "type": "text", "primaryKey": false, "notNull": false}, "db_key": {"name": "db_key", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"repos_organization_id_organization_id_fk": {"name": "repos_organization_id_organization_id_fk", "tableFrom": "repos", "tableTo": "organization", "schemaTo": "public", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"repos_org_slug_unique": {"columns": ["organization_id", "slug"], "nullsNotDistinct": false, "name": "repos_org_slug_unique"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.subscription": {"name": "subscription", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "plan": {"name": "plan", "type": "text", "primaryKey": false, "notNull": true}, "reference_id": {"name": "reference_id", "type": "text", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'incomplete'"}, "period_start": {"name": "period_start", "type": "timestamp", "primaryKey": false, "notNull": false}, "period_end": {"name": "period_end", "type": "timestamp", "primaryKey": false, "notNull": false}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "boolean", "primaryKey": false, "notNull": false}, "seats": {"name": "seats", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"columns": ["email"], "nullsNotDistinct": false, "name": "user_email_unique"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.public_projects": {"name": "public_projects", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "repository_url": {"name": "repository_url", "type": "text", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "db_url": {"name": "db_url", "type": "text", "primaryKey": false, "notNull": false}, "db_key": {"name": "db_key", "type": "text", "primaryKey": false, "notNull": false}, "latest_job_id": {"name": "latest_job_id", "type": "text", "primaryKey": false, "notNull": false}, "latest_job_status": {"name": "latest_job_status", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"public_projects_slug_key": {"columns": ["slug"], "nullsNotDistinct": false, "name": "public_projects_slug_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "active_organization_id": {"name": "active_organization_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"columns": ["token"], "nullsNotDistinct": false, "name": "session_token_unique"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}}, "enums": {"public.context_type_enum": {"name": "context_type_enum", "values": ["file", "page", "none"], "schema": "public"}, "public.extension_type": {"name": "extension_type", "values": ["vscode", "chrome", "slack", "gmail", "outlook", "discord", "notion", "linear", "github_app", "jetbrains", "other"], "schema": "public"}, "public.message_role": {"name": "message_role", "values": ["user", "assistant", "system"], "schema": "public"}, "public.plan_type": {"name": "plan_type", "values": ["free", "plus", "enterprise"], "schema": "public"}, "public.platform_type": {"name": "platform_type", "values": ["vscode", "chrome"], "schema": "public"}, "public.token_state": {"name": "token_state", "values": ["active", "revoked", "expired"], "schema": "public"}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}}}