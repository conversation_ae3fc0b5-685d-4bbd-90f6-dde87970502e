################################################################

NODE_ENV=development
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/app
INGESTION_API_URL=http://0.0.0.0:8000

################################################################

# You must set the following environment variables for the application to run

# Absolute path to directory where ingested repos will be stored (clones + DBs)
# E.g. "/Users/<USER>/Documents/autodocs/ingested_repos"
ANALYSIS_DB_DIR=

OPENAI_API_KEY=<your_openai_api_key>

# Optional: GitHub token to access private repositories
GITHUB_TOKEN=<your_github_token>

# The following are customizable for summaries and embeddings (OpenAI-compatible APIs)

SUMMARIES_BASE_URL=https://openrouter.ai/api/v1
SUMMARIES_MODEL=google/gemini-2.5-flash
SUMMARIES_API_KEY=<your_summaries_api_key>

# Default Gemini rate limits
MAX_REQUESTS_PER_SECOND=15

EMBEDDINGS_BASE_URL=https://api.openai.com/v1
EMBEDDINGS_MODEL=text-embedding-3-large
EMBEDDINGS_API_KEY=<your_embeddings_api_key>

